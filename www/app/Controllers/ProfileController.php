<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\RegisterModel;
use App\Models\SchoolsModel;
use App\Models\UsersModel;
use App\Models\UsersgroupsModel;
use App\Models\GradesModel;
use App\Models\GradesusersModel;
use App\Models\TeacherModel;

use CodeIgniter\HTTP\ResponseInterface;

class ProfileController extends BaseController
{

    public function Perfil()
    {
      $this->ionAuth    = new \IonAuth\Libraries\IonAuth();
      // CARGAR DATA DE USUARIO
      $id = $_SESSION['user_id'];
      $UsersModel = new UsersModel();
      $user_data = $UsersModel->find($id);
      $request = \Config\Services::request();
      $salida = [];
      //validate if its post request
      $method = $request->getMethod();
      if($method == "post"){
        $data = [
					'first_name' => $request->getPost('first_name'),
					'last_name'  => $request->getPost('last_name'),
					'email'    => $request->getPost('email'),
					'phone'      => $request->getPost('phone'),
          'rut'        => $request->getPost('rut'),
				];

        if ($request->getPost('password'))
				{
          // validate password are the same
          if ($request->getPost('password') !== $request->getPost('password_confirm'))
          {
            $salida['ERROR'] = 'Las contraseñas no coinciden';
          }else{
            $data['password'] = $this->request->getPost('password');
          }

				}


        // Verificar si se ha enviado un archivo
        if($this->request->getFile('file') && $this->request->getFile('file')->isValid()) {
          $file = $this->request->getFile('file');

          // Validar que sea una imagen
          $validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
          if (!in_array($file->getMimeType(), $validTypes)) {
            $salida['ERROR'] = 'Solo se permiten archivos de imagen (JPG, PNG, GIF)';
            // Mantener la imagen actual si existe
            if (!empty($user_data['profile_img'])) {
              $data['profile_img'] = $user_data['profile_img'];
            }
          }
          // Validar tamaño (máximo 2MB)
          else if ($file->getSize() > 2097152) {
            $salida['ERROR'] = 'La imagen no debe superar los 2MB';
            // Mantener la imagen actual si existe
            if (!empty($user_data['profile_img'])) {
              $data['profile_img'] = $user_data['profile_img'];
            }
          }
          else {
            // Procesar la imagen
            $new_file_name = md5(time()).".".$file->getExtension();

            // Redimensionar la imagen si es necesario
            $image = \Config\Services::image();

            // Mover el archivo a una ubicación temporal
            $file->move(ROOTPATH . 'public/assets/uploads/images/temp', $new_file_name);
            $imagePath = ROOTPATH . 'public/assets/uploads/images/temp/' . $new_file_name;

            // Redimensionar manteniendo la proporción si excede 300x300
            $image->withFile($imagePath)
                  ->resize(300, 300, true, 'height')
                  ->save(ROOTPATH . 'public/assets/uploads/images/profiles/' . $new_file_name);

            // Eliminar el archivo temporal
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            $data['profile_img'] = $new_file_name;
          }
        } else if ($request->getPost('remove_image') == '1') {
          // Si se solicita eliminar la imagen
          $data['profile_img'] = '';

          // Eliminar el archivo físico si existe
          if (!empty($user_data['profile_img'])) {
            $oldImagePath = ROOTPATH . 'public/assets/uploads/images/profiles/' . $user_data['profile_img'];
            if (file_exists($oldImagePath)) {
                unlink($oldImagePath);
            }
          }
        } else {
          // Si no se sube una nueva imagen y no se solicita eliminar, mantener la actual
          if (!empty($user_data['profile_img'])) {
            $data['profile_img'] = $user_data['profile_img'];
          }
        }

        // echo print_r($salida,true); die;
        if ($this->ionAuth->update($id, $data))
				{
					 $salida['SUCCESS'] = 'Usuario actualizado correctamente';
					 // Recargar los datos del usuario después de la actualización
					 $user_data = $UsersModel->find($id);
				}
				else
				{
					$salida['ERROR'] = 'Error al actualizar usuario';
				}

      }

      $docenteBooksGroupBy = getTeacherBooksGroupBy();
      $docenteBooksGroupBy = json_encode($docenteBooksGroupBy);

      $output = view('perfil', array(
        'data' => $user_data,
        'salida' => $salida
        ));
        $salida['CONTENT'] = $output;
        $salida['docenteBooksGroupBy'] = $docenteBooksGroupBy;
         return theme($salida);
    }


}
